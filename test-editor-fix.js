#!/usr/bin/env node

/**
 * 测试editor修复的脚本
 * 验证VIEWER错误是否已修复
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 测试editor项目VIEWER错误修复...\n');

// 测试1: 检查PermissionService.ts中的CollaborationRole定义
function testCollaborationRoleDefinition() {
  console.log('📋 测试1: 检查CollaborationRole枚举定义');
  
  const permissionServicePath = path.join(__dirname, 'editor/src/services/PermissionService.ts');
  
  if (!fs.existsSync(permissionServicePath)) {
    console.log('❌ PermissionService.ts文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(permissionServicePath, 'utf8');
  
  // 检查是否包含CollaborationRole枚举定义
  const hasCollaborationRole = content.includes('export enum CollaborationRole');
  const hasViewer = content.includes('VIEWER = \'viewer\'');
  const hasEditor = content.includes('EDITOR = \'editor\'');
  const hasAdmin = content.includes('ADMIN = \'admin\'');
  const hasOwner = content.includes('OWNER = \'owner\'');
  
  if (hasCollaborationRole && hasViewer && hasEditor && hasAdmin && hasOwner) {
    console.log('✅ CollaborationRole枚举定义正确');
    return true;
  } else {
    console.log('❌ CollaborationRole枚举定义不完整');
    return false;
  }
}

// 测试2: 检查CollaborationService.ts中的导入
function testCollaborationServiceImport() {
  console.log('📋 测试2: 检查CollaborationService.ts导入');
  
  const collaborationServicePath = path.join(__dirname, 'editor/src/services/CollaborationService.ts');
  
  if (!fs.existsSync(collaborationServicePath)) {
    console.log('❌ CollaborationService.ts文件不存在');
    return false;
  }
  
  const content = fs.readFileSync(collaborationServicePath, 'utf8');
  
  // 检查是否正确导入CollaborationRole
  const hasImport = content.includes('import { CollaborationRole } from \'./PermissionService\'');
  const hasExport = content.includes('export { CollaborationRole }');
  
  // 检查是否没有重复定义
  const hasOldDefinition = content.includes('export enum CollaborationRole');
  
  if (hasImport && hasExport && !hasOldDefinition) {
    console.log('✅ CollaborationService.ts导入正确');
    return true;
  } else {
    console.log('❌ CollaborationService.ts导入有问题');
    if (!hasImport) console.log('   - 缺少import语句');
    if (!hasExport) console.log('   - 缺少export语句');
    if (hasOldDefinition) console.log('   - 仍有重复的枚举定义');
    return false;
  }
}

// 测试3: 检查DEFAULT_ROLE_PERMISSIONS使用
function testDefaultRolePermissions() {
  console.log('📋 测试3: 检查DEFAULT_ROLE_PERMISSIONS使用');
  
  const permissionServicePath = path.join(__dirname, 'editor/src/services/PermissionService.ts');
  const content = fs.readFileSync(permissionServicePath, 'utf8');
  
  // 检查是否正确使用CollaborationRole.VIEWER
  const hasViewerUsage = content.includes('[CollaborationRole.VIEWER]:');
  const hasEditorUsage = content.includes('[CollaborationRole.EDITOR]:');
  const hasAdminUsage = content.includes('[CollaborationRole.ADMIN]:');
  
  if (hasViewerUsage && hasEditorUsage && hasAdminUsage) {
    console.log('✅ DEFAULT_ROLE_PERMISSIONS使用正确');
    return true;
  } else {
    console.log('❌ DEFAULT_ROLE_PERMISSIONS使用有问题');
    return false;
  }
}

// 测试4: 检查构建产物
function testBuildOutput() {
  console.log('📋 测试4: 检查构建产物');
  
  const distPath = path.join(__dirname, 'editor/dist');
  const indexPath = path.join(distPath, 'index.html');
  const assetsPath = path.join(distPath, 'assets');
  
  if (!fs.existsSync(distPath)) {
    console.log('❌ dist目录不存在，请运行 npm run build');
    return false;
  }
  
  if (!fs.existsSync(indexPath)) {
    console.log('❌ index.html不存在');
    return false;
  }
  
  if (!fs.existsSync(assetsPath)) {
    console.log('❌ assets目录不存在');
    return false;
  }
  
  const assetsFiles = fs.readdirSync(assetsPath);
  if (assetsFiles.length === 0) {
    console.log('❌ assets目录为空');
    return false;
  }
  
  console.log('✅ 构建产物存在');
  console.log(`   - 资源文件数量: ${assetsFiles.length}`);
  return true;
}

// 测试5: 检查其他文件的导入更新
function testOtherFilesImport() {
  console.log('📋 测试5: 检查其他文件的导入更新');
  
  const filesToCheck = [
    'editor/src/components/collaboration/UserList.tsx',
    'editor/src/components/collaboration/PermissionPanel.tsx',
    'editor/src/store/collaboration/permissionSlice.ts'
  ];
  
  let allCorrect = true;
  
  for (const filePath of filesToCheck) {
    const fullPath = path.join(__dirname, filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`❌ 文件不存在: ${filePath}`);
      allCorrect = false;
      continue;
    }
    
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // 检查是否从PermissionService导入CollaborationRole
    const hasCorrectImport = content.includes('CollaborationRole') &&
                           (content.includes('from \'../../services/PermissionService\'') ||
                            content.includes('from \'./PermissionService\''));

    // 检查是否还有旧的导入（同时导入CollaborationRole和CollaborationService）
    const hasOldImport = content.includes('{ CollaborationRole, CollaborationUser }') &&
                        content.includes('from \'../../services/CollaborationService\'');
    
    if (hasCorrectImport && !hasOldImport) {
      console.log(`✅ ${path.basename(filePath)} 导入正确`);
    } else {
      console.log(`❌ ${path.basename(filePath)} 导入有问题`);
      allCorrect = false;
    }
  }
  
  return allCorrect;
}

// 运行所有测试
function runAllTests() {
  const tests = [
    testCollaborationRoleDefinition,
    testCollaborationServiceImport,
    testDefaultRolePermissions,
    testBuildOutput,
    testOtherFilesImport
  ];
  
  let passedTests = 0;
  
  for (const test of tests) {
    try {
      if (test()) {
        passedTests++;
      }
    } catch (error) {
      console.log(`❌ 测试执行出错: ${error.message}`);
    }
    console.log('');
  }
  
  console.log('📊 测试结果总结:');
  console.log(`✅ 通过: ${passedTests}/${tests.length}`);
  console.log(`❌ 失败: ${tests.length - passedTests}/${tests.length}`);
  
  if (passedTests === tests.length) {
    console.log('\n🎉 所有测试通过！VIEWER错误已成功修复！');
    console.log('\n💡 建议下一步操作:');
    console.log('   1. 运行 .\\start-windows.ps1 启动所有服务');
    console.log('   2. 访问 http://localhost:80 测试编辑器');
    console.log('   3. 检查浏览器控制台是否还有错误');
  } else {
    console.log('\n⚠️  部分测试失败，请检查上述错误信息');
  }
}

// 执行测试
runAllTests();
